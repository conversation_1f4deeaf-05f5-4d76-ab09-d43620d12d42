import { useState, useEffect } from 'react'
import { CustomerToken, OAuth2Provider } from '@rpa-project/shared'
import { customerApi, oauth2Api } from '../../services/api'
import { CustomerTokenForm } from './CustomerTokenForm'

interface CustomerTokenListProps {
  customerId: string
  customerName: string
}

export function CustomerTokenList({ customerId, customerName }: CustomerTokenListProps) {
  const [tokens, setTokens] = useState<CustomerToken[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [editingToken, setEditingToken] = useState<CustomerToken | null>(null)

  useEffect(() => {
    loadTokens()
  }, [customerId])

  const loadTokens = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await customerApi.getCustomerTokenList(customerId)
      if (response.success && response.data) {
        setTokens(response.data)
      } else {
        setError(response.error || 'Misslyckades att ladda tokens')
      }
    } catch (err) {
      setError('Misslyckades att ladda tokens')
      console.error('Error loading customer tokens:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateToken = () => {
    setEditingToken(null)
    setShowForm(true)
  }

  const handleEditToken = (token: CustomerToken) => {
    setEditingToken(token)
    setShowForm(true)
  }

  const handleDeleteToken = async (tokenId: string, tokenName: string) => {
    if (!confirm(`Är du säker på att du vill ta bort token "${tokenName}"?`)) {
      return
    }

    try {
      const response = await customerApi.deleteCustomerToken(customerId, tokenId)
      if (response.success) {
        await loadTokens()
      } else {
        setError(response.error || 'Misslyckades att ta bort token')
      }
    } catch (err) {
      setError('Misslyckades att ta bort token')
      console.error('Error deleting customer token:', err)
    }
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingToken(null)
    loadTokens()
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingToken(null)
  }

  const handleRefreshToken = async (tokenId: string, tokenName: string) => {
    if (!confirm(`Vill du förnya token "${tokenName}"?`)) {
      return
    }

    try {
      const response = await oauth2Api.refreshToken(tokenId)
      if (response.success) {
        await loadTokens() // Reload tokens to show updated expiration
        alert('Token förnyad framgångsrikt!')
      } else {
        alert(`Misslyckades att förnya token: ${response.error}`)
      }
    } catch (err) {
      console.error('Error refreshing token:', err)
      alert('Misslyckades att förnya token')
    }
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('sv-SE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  if (loading) {
    return (
      <div style={{ padding: '2rem', textAlign: 'center' }}>
        <p>Laddar tokens...</p>
      </div>
    )
  }

  return (
    <div>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '1.5rem' 
      }}>
        <div>
          <h2 className="section-title">API Tokens för {customerName}</h2>
          <p className="dashboard-subtitle section-subtitle">
            Hantera API tokens och refresh tokens för denna kund
          </p>
        </div>
        <button
          onClick={handleCreateToken}
          className="action-button primary"
        >
          <span>Ny token</span>
        </button>
      </div>

      {error && (
        <div className="error-card" style={{ marginBottom: '1rem' }}>
          <h3 className="error-title">Fel</h3>
          <p className="error-message">{error}</p>
        </div>
      )}

      {/* Tokens List */}
      {tokens.length === 0 ? (
        <div className="empty-state-container">
          <div className="empty-state">
            <div className="empty-state-icon">🔑</div>
            <p className="empty-state-title">Inga tokens än</p>
            <p className="empty-state-subtitle">Skapa din första API token för denna kund</p>
            <button
              onClick={handleCreateToken}
              className="action-button primary centered"
            >
              <span>Skapa första token</span>
            </button>
          </div>
        </div>
      ) : (
        <div className="table-container">
          <div className="activity-table">
            <table className="table">
              <thead>
                <tr>
                  <th>Namn</th>
                  <th>Provider</th>
                  <th>Status</th>
                  <th>API Token</th>
                  <th>Refresh Token</th>
                  <th>Skapad</th>
                  <th>Åtgärder</th>
                </tr>
              </thead>
              <tbody>
                {tokens.map((token) => {
                  const getProviderDisplayName = (provider: OAuth2Provider) => {
                    switch (provider) {
                      case 'eEkonomi': return 'eEkonomi'
                      case 'Fortnox': return 'Fortnox'
                      case 'manual': return 'Manuell'
                      default: return provider
                    }
                  }

                  const getTokenStatus = () => {
                    if (token.provider !== 'manual' && token.isExpired) {
                      return { text: 'Utgången', className: 'status-failed' }
                    }
                    if (token.hasApiToken) {
                      return { text: 'Aktiv', className: 'status-completed' }
                    }
                    return { text: 'Ej konfigurerad', className: 'status-pending' }
                  }

                  const status = getTokenStatus()

                  return (
                  <tr key={token.id}>
                    <td>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                        <span>🔑</span>
                        <span className="font-medium">{token.name}</span>
                      </div>
                    </td>
                    <td>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                        {token.provider === 'eEkonomi' && <span>🏢</span>}
                        {token.provider === 'Fortnox' && <span>📊</span>}
                        {token.provider === 'manual' && <span>✋</span>}
                        <span>{getProviderDisplayName(token.provider)}</span>
                      </div>
                    </td>
                    <td>
                      <button className={`status-button ${status.className}`}>
                        <span>{status.text}</span>
                      </button>
                      {token.expiresAt && (
                        <div style={{ fontSize: '0.75rem', color: '#6b7280', marginTop: '0.25rem' }}>
                          Går ut: {formatDate(token.expiresAt)}
                        </div>
                      )}
                    </td>
                    <td className="secondary-text">
                      {token.description || '—'}
                    </td>
                    <td>
                      {token.hasApiToken ? (
                        <button className="status-button status-completed">
                          <span>Konfigurerad</span>
                        </button>
                      ) : (
                        <span>—</span>
                      )}
                    </td>
                    <td>
                      {token.hasRefreshToken ? (
                        <button className="status-button status-completed">
                          <span>Konfigurerad</span>
                        </button>
                      ) : (
                        <span>—</span>
                      )}
                    </td>
                    <td className="secondary-text">
                      {formatDate(token.createdAt)}
                    </td>
                    <td>
                      <div className="flow-actions">
                        {/* Refresh button for OAuth2 tokens with refresh token */}
                        {token.provider !== 'manual' && token.hasRefreshToken && (
                          <button
                            onClick={() => handleRefreshToken(token.id, token.name)}
                            className="action-button-small icon-only"
                            title="Förnya token"
                          >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                              <path d="M21 3v5h-5"></path>
                              <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                              <path d="M3 21v-5h5"></path>
                            </svg>
                          </button>
                        )}
                        <button
                          onClick={() => handleEditToken(token)}
                          className="action-button-small icon-only"
                          title="Redigera token"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                            <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                          </svg>
                        </button>
                        <button
                          onClick={() => handleDeleteToken(token.id, token.name)}
                          className="action-button-small danger"
                          title="Ta bort token"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="3,6 5,6 21,6"></polyline>
                            <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Token Form Modal */}
      {showForm && (
        <CustomerTokenForm
          customerId={customerId}
          token={editingToken}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      )}
    </div>
  )
}
