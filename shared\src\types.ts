// Base types for RPA automation

export interface RpaStepBase {
  id: string;
  type: string;
  description?: string;
  timeout?: number; // milliseconds, default 30000
}

// Navigation steps
export interface NavigateStep extends RpaStepBase {
  type: 'navigate';
  url: string;
  waitUntil?: 'load' | 'domcontentloaded' | 'networkidle';
}

export interface GoBackStep extends RpaStepBase {
  type: 'goBack';
}

export interface GoForwardStep extends RpaStepBase {
  type: 'goForward';
}

export interface ReloadStep extends RpaStepBase {
  type: 'reload';
}

// Interaction steps
export interface ClickStep extends RpaStepBase {
  type: 'click';
  selector: string;
  button?: 'left' | 'right' | 'middle';
  clickCount?: number;
  force?: boolean;
}

export interface FillStep extends RpaStepBase {
  type: 'fill';
  selector: string;
  value: string;
  force?: boolean;
}

export interface TypeStep extends RpaStepBase {
  type: 'type';
  selector: string;
  text: string;
  delay?: number; // milliseconds between keystrokes
}

export interface SelectOptionStep extends RpaStepBase {
  type: 'selectOption';
  selector: string;
  value?: string;
  label?: string;
  index?: number;
}

export interface CheckStep extends RpaStepBase {
  type: 'check';
  selector: string;
  force?: boolean;
}

export interface UncheckStep extends RpaStepBase {
  type: 'uncheck';
  selector: string;
  force?: boolean;
}

// Wait steps
export interface WaitForSelectorStep extends RpaStepBase {
  type: 'waitForSelector';
  selector: string;
  state?: 'attached' | 'detached' | 'visible' | 'hidden';
}

export interface WaitForTimeoutStep extends RpaStepBase {
  type: 'waitForTimeout';
  duration: number; // milliseconds
}

export interface WaitForUrlStep extends RpaStepBase {
  type: 'waitForUrl';
  url: string | RegExp;
}

// Extraction steps
export interface ExtractTextStep extends RpaStepBase {
  type: 'extractText';
  selector: string;
  variableName: string;
}

export interface ExtractAttributeStep extends RpaStepBase {
  type: 'extractAttribute';
  selector: string;
  attribute: string;
  variableName: string;
}

export interface TakeScreenshotStep extends RpaStepBase {
  type: 'takeScreenshot';
  path?: string;
  fullPage?: boolean;
  variableName?: string; // Variable name to store base64 encoded image
}

// Conditional steps
export interface IfElementExistsStep extends RpaStepBase {
  type: 'ifElementExists';
  selector: string;
  thenSteps: RpaStep[];
  elseSteps?: RpaStep[];
}

export interface ConditionalClickStep extends RpaStepBase {
  type: 'conditionalClick';
  selector: string;
  condition: 'exists' | 'enabled' | 'disabled';
  clickIfTrue?: boolean; // default true
  button?: 'left' | 'right' | 'middle';
  force?: boolean;
}

// Credential steps
export interface FillPasswordStep extends RpaStepBase {
  type: 'fillPassword';
  selector: string;
  credentialId: string;
  force?: boolean;
}

export interface Fill2FAStep extends RpaStepBase {
  type: 'fill2FA';
  selector: string;
  credentialId: string;
  force?: boolean;
}

export interface DownloadFileStep extends RpaStepBase {
  type: 'downloadFile';
  triggerSelector?: string; // Optional selector to click to trigger download
  filename?: string; // Optional custom filename, if not provided uses suggested filename
  variableName?: string; // Variable name to store base64 content
  saveToFile?: boolean; // Whether to save file to disk (default: false)
  forceDownload?: boolean; // Whether to add download attribute to force download instead of opening in browser
}

export interface ExtractPdfTextStep extends RpaStepBase {
  type: 'extractPdfText';
  base64Input: string; // Base64 PDF data or variable reference
  prompt: string; // User prompt for LLM processing
  variableName?: string; // Variable name to store AI response (default: 'var-ai-response')
}

// Union type for all step types
export type RpaStep =
  | NavigateStep
  | GoBackStep
  | GoForwardStep
  | ReloadStep
  | ClickStep
  | FillStep
  | TypeStep
  | SelectOptionStep
  | CheckStep
  | UncheckStep
  | WaitForSelectorStep
  | WaitForTimeoutStep
  | WaitForUrlStep
  | ExtractTextStep
  | ExtractAttributeStep
  | TakeScreenshotStep
  | IfElementExistsStep
  | ConditionalClickStep
  | FillPasswordStep
  | Fill2FAStep
  | DownloadFileStep
  | ExtractPdfTextStep;

// Flow definition
export interface RpaFlow {
  id: string;
  name: string;
  description?: string;
  customerId: string;
  steps: RpaStep[];
  variables?: Record<string, any>;
  settings?: FlowSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface FlowSettings {
  browser?: 'chromium' | 'firefox' | 'webkit';
  headless?: boolean;
  viewport?: {
    width: number;
    height: number;
  };
  userAgent?: string;
  locale?: string;
  timezone?: string;
}

// Credential types
export interface CredentialBase {
  id: string;
  name: string;
  description?: string;
  type: 'password' | '2fa';
  createdAt: Date;
  updatedAt: Date;
}

export interface PasswordCredential extends CredentialBase {
  type: 'password';
  username: string;
  // password is encrypted and stored separately
}

export interface TwoFactorCredential extends CredentialBase {
  type: '2fa';
  // secret is encrypted and stored separately
}

export type Credential = PasswordCredential | TwoFactorCredential;

// API request/response types for credentials
export interface CreatePasswordCredentialRequest {
  name: string;
  description?: string;
  username: string;
  password: string;
}

export interface CreateTwoFactorCredentialRequest {
  name: string;
  description?: string;
  secret: string;
}

export type CreateCredentialRequest = CreatePasswordCredentialRequest | CreateTwoFactorCredentialRequest;

export interface UpdatePasswordCredentialRequest {
  name?: string;
  description?: string;
  username?: string;
  password?: string;
}

export interface UpdateTwoFactorCredentialRequest {
  name?: string;
  description?: string;
  secret?: string;
}

// Customer types
export interface Customer {
  id: string;
  customerNumber: string;
  name: string;
  vismaNumber?: string;
  createdAt: Date;
  updatedAt: Date;
}

// OAuth2 Provider types
export type OAuth2Provider = 'eEkonomi' | 'Fortnox' | 'manual';

export interface OAuth2Config {
  clientId: string;
  clientSecret: string;
  authUrl: string;
  tokenUrl: string;
  scopes: string[];
  redirectUri: string;
}

// Customer token types
export interface CustomerToken {
  id: string;
  customerId: string;
  name: string;
  description?: string;
  provider: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  hasApiToken?: boolean;
  hasRefreshToken?: boolean;
  expiresAt?: Date;
  isExpired?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Customer request types
export interface CreateCustomerRequest {
  customerNumber: string;
  name: string;
  vismaNumber?: string;
}

export interface UpdateCustomerRequest {
  customerNumber?: string;
  name?: string;
  vismaNumber?: string;
}

// Customer token request types
export interface CreateCustomerTokenRequest {
  name: string;
  description?: string;
  provider: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
}

export interface UpdateCustomerTokenRequest {
  name?: string;
  description?: string;
  provider?: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
}

// OAuth2 specific request types
export interface InitiateOAuth2Request {
  customerId: string;
  provider: OAuth2Provider;
  tokenName: string;
  description?: string;
}

export interface OAuth2CallbackRequest {
  code: string;
  state: string;
}

export interface OAuth2TokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
}

export type UpdateCredentialRequest = UpdatePasswordCredentialRequest | UpdateTwoFactorCredentialRequest;

// Execution types
export interface FlowExecution {
  id: string;
  flowId: string;
  status: ExecutionStatus;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
  logs: ExecutionLog[];
  results?: Record<string, any>;
}

export type ExecutionStatus = 
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface ExecutionLog {
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  stepId?: string;
  data?: any;
}

// API types
export interface CreateFlowRequest {
  name: string;
  description?: string;
  customerId: string;
  steps: RpaStep[];
  settings?: FlowSettings;
}

export interface UpdateFlowRequest {
  name?: string;
  description?: string;
  customerId?: string;
  steps?: RpaStep[];
  settings?: FlowSettings;
}

export interface ExecuteFlowRequest {
  flowId: string;
  variables?: Record<string, any>;
}

// Schedule types
export interface FlowSchedule {
  id: string;
  flowId: string;
  name: string;
  description?: string;
  cronExpression: string;
  timezone: string;
  enabled: boolean;
  variables?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  lastRunAt?: Date;
  nextRunAt?: Date;
}

export interface CreateScheduleRequest {
  flowId: string;
  name: string;
  description?: string;
  cronExpression: string;
  timezone?: string;
  enabled?: boolean;
  variables?: Record<string, any>;
}

export interface UpdateScheduleRequest {
  name?: string;
  description?: string;
  cronExpression?: string;
  timezone?: string;
  enabled?: boolean;
  variables?: Record<string, any>;
}

export interface ScheduleQuery {
  flowId?: string;
  enabled?: boolean;
  limit?: number;
  offset?: number;
}

// Cron expression validation and parsing
export interface CronExpressionInfo {
  expression: string;
  description: string;
  nextRuns: Date[];
  isValid: boolean;
  error?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// React Flow types for frontend
export interface FlowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    step: RpaStep;
    label: string;
  };
}

export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
}

export interface FlowData {
  nodes: FlowNode[];
  edges: FlowEdge[];
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

// Utility function for generating IDs
export function generateId(): string {
  return Math.random().toString(36).substring(2, 11) + Date.now().toString(36)
}

// Utility functions for schedules
export function createEmptySchedule(flowId: string, name: string): FlowSchedule {
  return {
    id: generateId(),
    flowId,
    name,
    description: '',
    cronExpression: '0 9 * * 1-5', // Default: 9 AM on weekdays
    timezone: 'UTC',
    enabled: true,
    variables: {},
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

// Common cron expressions
export const COMMON_CRON_EXPRESSIONS = {
  'Every minute': '* * * * *',
  'Every 5 minutes': '*/5 * * * *',
  'Every 15 minutes': '*/15 * * * *',
  'Every 30 minutes': '*/30 * * * *',
  'Every hour': '0 * * * *',
  'Every 2 hours': '0 */2 * * *',
  'Every 6 hours': '0 */6 * * *',
  'Every 12 hours': '0 */12 * * *',
  'Daily at 9 AM': '0 9 * * *',
  'Daily at 6 PM': '0 18 * * *',
  'Weekdays at 9 AM': '0 9 * * 1-5',
  'Weekends at 10 AM': '0 10 * * 6,0',
  'Weekly on Monday at 9 AM': '0 9 * * 1',
  'Monthly on 1st at 9 AM': '0 9 1 * *',
  'Yearly on Jan 1st at 9 AM': '0 9 1 1 *'
} as const

export function getCronDescription(cronExpression: string): string {
  // Find matching common expression
  for (const [description, expression] of Object.entries(COMMON_CRON_EXPRESSIONS)) {
    if (expression === cronExpression) {
      return description
    }
  }

  // Return the expression itself if no match found
  return cronExpression
}

// Utility functions for creating steps
export function createEmptyFlow(name: string, customerId: string = ''): RpaFlow {
  return {
    id: generateId(),
    name,
    description: '',
    customerId,
    steps: [],
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

export function createStepFromType(stepType: string): RpaStep {
  const baseStep = {
    id: generateId(),
    type: stepType as any,
    timeout: 30000
  }

  switch (stepType) {
    case 'navigate':
      return { ...baseStep, type: 'navigate', url: 'https://example.com', waitUntil: 'load' }
    case 'goBack':
      return { ...baseStep, type: 'goBack' }
    case 'goForward':
      return { ...baseStep, type: 'goForward' }
    case 'reload':
      return { ...baseStep, type: 'reload' }
    case 'click':
      return { ...baseStep, type: 'click', selector: 'button', button: 'left' }
    case 'fill':
      return { ...baseStep, type: 'fill', selector: 'input', value: 'placeholder text' }
    case 'type':
      return { ...baseStep, type: 'type', selector: 'input', text: 'text to type', delay: 0 }
    case 'selectOption':
      return { ...baseStep, type: 'selectOption', selector: 'select', value: 'option-value' }
    case 'check':
      return { ...baseStep, type: 'check', selector: 'input[type="checkbox"]' }
    case 'uncheck':
      return { ...baseStep, type: 'uncheck', selector: 'input[type="checkbox"]' }
    case 'waitForSelector':
      return { ...baseStep, type: 'waitForSelector', selector: 'element', state: 'visible' }
    case 'waitForTimeout':
      return { ...baseStep, type: 'waitForTimeout', duration: 1000 }
    case 'extractText':
      return { ...baseStep, type: 'extractText', selector: 'element', variableName: 'extractedText' }
    case 'takeScreenshot':
      return { ...baseStep, type: 'takeScreenshot', fullPage: false }
    case 'conditionalClick':
      return { ...baseStep, type: 'conditionalClick', selector: 'button', condition: 'exists', clickIfTrue: true, button: 'left' }
    case 'fillPassword':
      return { ...baseStep, type: 'fillPassword', selector: 'input[type="password"]', credentialId: '' }
    case 'fill2FA':
      return { ...baseStep, type: 'fill2FA', selector: 'input', credentialId: '' }
    case 'downloadFile':
      return { ...baseStep, type: 'downloadFile', triggerSelector: 'a[href*=".pdf"]', filename: '', variableName: '', saveToFile: false, forceDownload: false }
    case 'extractPdfText':
      return { ...baseStep, type: 'extractPdfText', base64Input: '', prompt: 'Extrahera all text från dokumentet', variableName: 'var-ai-response' }
    default:
      throw new Error(`Unknown step type: ${stepType}`)
  }
}

export function getStepLabel(step: RpaStep): string {
  switch (step.type) {
    case 'navigate':
      return step.url || 'Navigate to URL'
    case 'goBack':
      return 'Go back'
    case 'goForward':
      return 'Go forward'
    case 'reload':
      return 'Reload page'
    case 'click':
      return step.selector || 'Click element'
    case 'fill':
      return `Fill: ${step.selector || 'element'}`
    case 'type':
      return `Type: ${step.text || 'text'}`
    case 'selectOption':
      return `Select: ${step.value || step.label || (step.index !== undefined ? `index ${step.index}` : 'option')}`
    case 'check':
      return `Check: ${step.selector || 'checkbox'}`
    case 'uncheck':
      return `Uncheck: ${step.selector || 'checkbox'}`
    case 'waitForSelector':
      return `Wait for: ${step.selector || 'element'}`
    case 'waitForTimeout':
      return `Wait ${step.duration || 1000}ms`
    case 'extractText':
      return `Extract: ${step.variableName || 'text'}`
    case 'takeScreenshot':
      return 'Take screenshot'
    case 'conditionalClick':
      const condStep = step as ConditionalClickStep
      const action = condStep.clickIfTrue ? 'Click if' : 'Click if not'
      return `${action} ${condStep.condition}: ${condStep.selector || 'element'}`
    case 'fillPassword':
      return `Fill password: ${step.selector || 'field'}`
    case 'fill2FA':
      return `Fill 2FA: ${step.selector || 'field'}`
    case 'downloadFile':
      const downloadStep = step as DownloadFileStep
      return downloadStep.triggerSelector
        ? `Download via: ${downloadStep.triggerSelector}`
        : 'Download file'
    case 'extractPdfText':
      const extractStep = step as ExtractPdfTextStep
      return `Extract PDF text: ${extractStep.variableName || 'var-ai-response'}`
    default:
      return step.type
  }
}



// React Flow conversion functions
export function convertFlowToReactFlow(flow: RpaFlow): FlowData {
  const nodes: FlowNode[] = flow.steps.map((step, index) => {
    // Create better positioning for AI-generated flows
    // Use a slight horizontal offset to prevent overlapping
    const baseX = 100
    const baseY = 50
    const verticalSpacing = 120
    const horizontalOffset = (index % 2) * 30 // Small alternating offset

    return {
      id: step.id,
      type: 'rpaStep',
      position: {
        x: baseX + horizontalOffset,
        y: baseY + (index * verticalSpacing)
      },
      data: {
        step,
        label: getStepLabel(step)
      }
    }
  })

  const edges: FlowEdge[] = []
  for (let i = 0; i < flow.steps.length - 1; i++) {
    edges.push({
      id: `${flow.steps[i].id}-${flow.steps[i + 1].id}`,
      source: flow.steps[i].id,
      target: flow.steps[i + 1].id,
      type: 'smoothstep'
    })
  }

  return { nodes, edges }
}

export function convertReactFlowToFlow(flowData: FlowData, flowId: string, flowName: string, customerId: string = ''): RpaFlow {
  // Build a map of connections
  const connections = new Map<string, string>()
  flowData.edges.forEach(edge => {
    connections.set(edge.source, edge.target)
  })

  // Find the starting node (no incoming edges)
  const hasIncoming = new Set(flowData.edges.map(e => e.target))
  const startNodes = flowData.nodes.filter(node => !hasIncoming.has(node.id))

  if (startNodes.length === 0 && flowData.nodes.length > 0) {
    // If no clear start, use first node
    const steps = flowData.nodes.map(node => node.data.step)
    return {
      id: flowId,
      name: flowName,
      description: '',
      customerId,
      steps,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }

  // Build ordered steps by following connections
  const steps: RpaStep[] = []
  const visited = new Set<string>()

  function addStepsFromNode(nodeId: string) {
    if (visited.has(nodeId)) return
    visited.add(nodeId)

    const node = flowData.nodes.find(n => n.id === nodeId)
    if (node) {
      steps.push(node.data.step)
      const nextNodeId = connections.get(nodeId)
      if (nextNodeId) {
        addStepsFromNode(nextNodeId)
      }
    }
  }

  if (startNodes.length > 0) {
    addStepsFromNode(startNodes[0].id)
  }

  return {
    id: flowId,
    name: flowName,
    description: '',
    customerId,
    steps,
    createdAt: new Date(),
    updatedAt: new Date()
  }
}
