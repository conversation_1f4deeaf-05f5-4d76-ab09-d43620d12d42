// Execution types
export interface FlowExecution {
  id: string;
  flowId: string;
  status: ExecutionStatus;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
  logs: ExecutionLog[];
  results?: Record<string, any>;
}

export type ExecutionStatus = 
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface ExecutionLog {
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  stepId?: string;
  data?: any;
}

// Credential types
export interface CredentialBase {
  id: string;
  name: string;
  description?: string;
  type: 'password' | '2fa';
  createdAt: Date;
  updatedAt: Date;
}

export interface PasswordCredential extends CredentialBase {
  type: 'password';
  username: string;
  // password is encrypted and stored separately
}

export interface TwoFactorCredential extends CredentialBase {
  type: '2fa';
  // secret is encrypted and stored separately
}

export type Credential = PasswordCredential | TwoFactorCredential;

// API request/response types for credentials
export interface CreatePasswordCredentialRequest {
  name: string;
  description?: string;
  username: string;
  password: string;
}

export interface CreateTwoFactorCredentialRequest {
  name: string;
  description?: string;
  secret: string;
}

export type CreateCredentialRequest = CreatePasswordCredentialRequest | CreateTwoFactorCredentialRequest;

export interface UpdatePasswordCredentialRequest {
  name?: string;
  description?: string;
  username?: string;
  password?: string;
}

export interface UpdateTwoFactorCredentialRequest {
  name?: string;
  description?: string;
  secret?: string;
}

export type UpdateCredentialRequest = UpdatePasswordCredentialRequest | UpdateTwoFactorCredentialRequest;

// Customer types
export interface Customer {
  id: string;
  customerNumber: string;
  name: string;
  vismaNumber?: string;
  createdAt: Date;
  updatedAt: Date;
}

// OAuth2 Provider types
export type OAuth2Provider = 'eEkonomi' | 'Fortnox' | 'manual';

export interface OAuth2Config {
  clientId: string;
  clientSecret: string;
  authUrl: string;
  tokenUrl: string;
  scopes: string[];
  redirectUri: string;
}

// Customer token types
export interface CustomerToken {
  id: string;
  customerId: string;
  name: string;
  description?: string;
  provider: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  hasApiToken?: boolean;
  hasRefreshToken?: boolean;
  expiresAt?: Date;
  isExpired?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Customer request types
export interface CreateCustomerRequest {
  customerNumber: string;
  name: string;
  vismaNumber?: string;
}

export interface UpdateCustomerRequest {
  customerNumber?: string;
  name?: string;
  vismaNumber?: string;
}

// Customer token request types
export interface CreateCustomerTokenRequest {
  name: string;
  description?: string;
  provider: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
}

export interface UpdateCustomerTokenRequest {
  name?: string;
  description?: string;
  provider?: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
}

// OAuth2 specific request types
export interface InitiateOAuth2Request {
  customerId: string;
  provider: OAuth2Provider;
  tokenName: string;
  description?: string;
}

export interface OAuth2CallbackRequest {
  code: string;
  state: string;
}

export interface OAuth2TokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
}
